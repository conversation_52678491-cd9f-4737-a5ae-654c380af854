struct Functions
{
    half FresnelSchlick(half VdotH, half F0)
    {
        return saturate(F0 + (1.0 - F0) * pow(1.0 - VdotH, 5.0));
    }
    half GeometrySchlickGGX(half NdotV, half r)
    {
        half k = (r + 1.0) * (r + 1.0) / 2.0;
        return NdotV / (NdotV * (1.0 - k) + k);
    }
    half GeometrySmithSchlickGGX(half NdotV, half NdotL, half r)
    {
        half ggx2 = GeometrySchlickGGX(NdotV, r);
        half ggx1 = GeometrySchlickGGX(NdotL, r);
        return ggx1 * ggx2;
    }
};
Functions functions;

half4 baseMap = Texture2DSample(_BaseMap, _BaseMapSampler, uv);
half4 SSSMap = Texture2DSample(_SSSMap, _SSSMapSampler, uv);
half4 speclularMap = Texture2DSample(_SpecMap, _SpecMapSampler, uv);
half insidelineMap = Texture2DSample(_OcclusionMap, _OcclusionMapSampler, uv2).r;
half3 extraMap = Texture2DSample(_ExtraMap, _ExtraMapSampler, uv);

half halfLambert = saturate(dot(normalWS, worldLightDir)) * 0.5 + 0.5;

half3 diffuse = 0;
half threshold = min(speclularMap.g, halfLambert);
half ramp1 = saturate(_ShadowAera  - threshold); 
half rampSmooth1 =  smoothstep(0, _ShadowSmooth, ramp1);
half rampSmooth = rampSmooth1;
diffuse = lerp(baseMap.rgb, SSSMap.rgb, rampSmooth * _ShadowMult);

half anisotropyMask = speclularMap.r;
half anisotropy = extraMap.r * 2 - 1;
half Un = pow(10,_Nu);
half Vn = pow(10,_Nv);
half3 N = normalWS;
half3 V = viewDirectionWS;
V = normalize(half3(0,0.75,0) + V);
half3 L = worldLightDir;
half3 H = normalize( V + L + half3(0,anisotropy * _AniPow - 0.5,0));
half3 T = normalize(tangentWS);
half3 B = normalize(bitangentWS);

half NdotV = saturate(dot(N, V));
half NdotL = saturate(dot(N, L));
half NdotH = saturate(dot(N, H));
half VdotH = saturate(dot(V, H));
half TdotH = (dot(T, H));
half BdotH = (dot(B, H));

half r = _Roughness * _Roughness;
half X = sqrt((Un + 1)*(Vn + 1)) / (8 * 3.1415926);
half Y = pow(NdotH,Un * ((TdotH * TdotH)/(1-NdotH*NdotH)) + Vn * ((BdotH * BdotH)/(1-NdotH*NdotH)));
half Z = saturate(VdotH * max(NdotL,NdotV)) * 0.95 + 0.05;
half F = functions.FresnelSchlick(VdotH,_F0);
half G = functions.GeometrySmithSchlickGGX(NdotV, NdotL, r * anisotropyMask);
half sepc = X * (Y/Z) * F * G;
sepc = saturate(sepc);
sepc = smoothstep(_SpecRemap.x,_SpecRemap.y,sepc);
half3 speclular = sepc * anisotropyMask * _SpecularColor.rgb * diffuse;

half2 viewOffset = normalize(worldLightDir + _RimDirection.xyz).xy;
viewOffset.x  = clamp(viewOffset.x, -_RimDirectionRange, _RimDirectionRange);
viewOffset.y  = clamp(viewOffset.y, -_RimDirectionRange, _RimDirectionRange);
half3 viewDir = viewDirectionWS;
viewDir.xy += viewOffset;
half f =  1.0h - saturate(dot(normalize(viewDir), normalWS));
half rim = smoothstep(_RimMin, _RimMax, f);
rim = smoothstep(0, _RimSmooth, rim);
rim *= 0.3h;// 头发的边缘光统一弱一些
half3 rimColor = rim * _RimMulti * _RimColor.rgb;

half3 emissionColor = _EmissionColor * speclularMap.b;
emissionColor += _SubEmissionColor;
half3 baseLight = diffuse + speclular + rimColor + emissionColor;
baseLight = lerp(_InsidelineColor, baseLight, insidelineMap);

return baseLight;