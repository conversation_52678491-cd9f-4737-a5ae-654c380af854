half4 baseMap = Texture2DSample(_BaseMap, _BaseMapSampler, uv);
half4 SSSMap = Texture2DSample(_SSSMap, _SSSMapSampler, uv);
half4 speclularMap = Texture2DSample(_SpecMap, _SpecMapSampler, uv);
half  insidelineMap = Texture2DSample(_OcclusionMap, _OcclusionMapSampler, uv2).r;

half stylizedHighlights = 1;
if(_EXTRATEX_ON == 1)
{
    half3 extraMap = Texture2DSample(_ExtraMap, _ExtraMapSampler, uv);
    stylizedHighlights = extraMap.r;
}

half _ShadowSmooth;
half _TextureShadowSmooth;
half _ShadowAera;
half _ShadowMult;
half _SpecularAera;
half _SpecularMulti;
half _SpecularGloss;
half _SpecularSmooth;
half _SpecularClearCloat;
half3 _InsidelineColor;

half3 halfDir = normalize(worldLightDir + viewDirectionWS);
half NdotL = saturate(dot(normalWS, worldLightDir));
half NdotV = saturate(dot(normalWS, viewDirectionWS));
half NdotH = saturate(dot(normalWS, halfDir));
half halfLambert = NdotL * 0.5 + 0.5;

half metal = 0;
half other = 0;
half leather = 0;
half rampV = 0;

if(SSSMap.a >= (0.9 - 0.08)) //其他
{
    //其他材质保留
    other = 1;
    rampV = 0.9;
    _ShadowSmooth = _ShadowSmooth5;
    _TextureShadowSmooth = _TextureShadowSmooth5;
    _ShadowAera = _ShadowAera5;
    _ShadowMult = _ShadowMult5;
    _SpecularAera = _SpecularAera5;
    _SpecularMulti = _SpecularMulti5;
    _SpecularGloss = _SpecularGloss5;
    _SpecularSmooth = _SpecularSmooth5;
    _SpecularClearCloat = _SpecularClearCloat5;
    _InsidelineColor = _InsidelineColor5;
}
else if(abs(SSSMap.a - 0.7) < 0.08)//皮革
{
    leather = 1;
    rampV = 0.7;
    _ShadowSmooth = _ShadowSmooth4;
    _TextureShadowSmooth = _TextureShadowSmooth4;
    _ShadowAera = _ShadowAera4;
    _ShadowMult = _ShadowMult4;
    _SpecularAera = _SpecularAera4;
    _SpecularMulti = _SpecularMulti4;
    _SpecularGloss = _SpecularGloss4;
    _SpecularSmooth = _SpecularSmooth4;
    _SpecularClearCloat = _SpecularClearCloat4;
    _InsidelineColor = _InsidelineColor4;
}
else if((abs(SSSMap.a - 0.5) < 0.1))//金属
{
    metal = 1 - abs(SSSMap.a - 0.5);//越接近0.5，金属性越强
    rampV = 0.5;
    _ShadowSmooth = _ShadowSmooth3;
    _TextureShadowSmooth = _TextureShadowSmooth3;
    _ShadowAera = _ShadowAera3;
    _ShadowMult = _ShadowMult3;
    _SpecularAera = _SpecularAera3;
    _SpecularMulti = _SpecularMulti3;
    _SpecularGloss = _SpecularGloss3;
    _SpecularSmooth = _SpecularSmooth3;
    _SpecularClearCloat = _SpecularClearCloat3;
    _InsidelineColor = _InsidelineColor3;
}
else if((abs(SSSMap.a - 0.3) < 0.1)) //布料
{
    rampV = 0.3;
    _ShadowSmooth = _ShadowSmooth2;
    _TextureShadowSmooth = _TextureShadowSmooth2;
    _ShadowAera = _ShadowAera2;
    _ShadowMult = _ShadowMult2;
    _SpecularAera = _SpecularAera2;
    _SpecularGloss = _SpecularGloss2;
    _SpecularSmooth = _SpecularSmooth2;
    _SpecularClearCloat = _SpecularClearCloat2;
    _InsidelineColor = _InsidelineColor2;
}
else if((abs(SSSMap.a - 0.1) < 0.08)) //皮肤
{
    rampV = 0.1;
    _ShadowSmooth = _ShadowSmooth1;
    _TextureShadowSmooth = _TextureShadowSmooth1;
    _ShadowAera = _ShadowAera1;
    _ShadowMult = _ShadowMult1;
    _SpecularAera = _SpecularAera1;
    _SpecularMulti = _SpecularMulti1;
    _SpecularGloss = _SpecularGloss1;
    _SpecularSmooth = _SpecularSmooth1;
    _SpecularClearCloat = _SpecularClearCloat1;
    _InsidelineColor = _InsidelineColor1;
}


half3 diffuse = 0;
half rampLambert = saturate(_ShadowAera  - halfLambert); 
half lambertSmooth =  smoothstep(0, _ShadowSmooth, rampLambert);

if(_RAMPTEX_ON == 1)
{
    half rampU = saturate(0.5 + _ShadowAera - NdotL);
    lambertSmooth = 1 - Texture2DSample(_RampMap, _RampMapSampler, half2(1 - rampU, 1 - rampV)).r;
}
half rampAO = saturate(_ShadowAera  - speclularMap.g); 
half AOSmooth =  smoothstep(0, _TextureShadowSmooth, rampAO);
half rampSmooth = max(lambertSmooth, AOSmooth);
diffuse = lerp(baseMap.rgb, SSSMap.rgb, rampSmooth * _ShadowMult);

half3 specular = 0;
half NdotHV = lerp(NdotH, NdotV, _SpecularClearCloat);
half specularSize = pow(NdotHV, (leather > 0.99 ? 0.5 : 1) * _SpecularGloss);
 half specularRange = max(0, smoothstep(-_SpecularSmooth, _SpecularSmooth, specularSize + _SpecularAera - 0.99)) * (leather > 0.99 ? 0.5 : speclularMap.r * stylizedHighlights);
specular = _SpecularMulti * diffuse * specularRange;

half3 sideSpecular = 0;
half sideSpecularSize = pow(NdotHV, (_SideSpecularGloss3 + _SpecularGloss / 2)) * metal ;
sideSpecular = sideSpecularSize * _SideSpecularColor3;//合并侧边高光到金属部位
sideSpecular *= (specularRange > 0.5 ? 0 : 1);
specular += sideSpecular;

sideSpecularSize = pow(NdotHV, (_SideSpecularGloss5 + _SpecularGloss / 2)) * other ;
sideSpecular = sideSpecularSize * _SideSpecularColor5;//合并侧边高光到其他部位
sideSpecular *= (specularRange > 0.5 ? 0 : 1) * speclularMap.r;
specular += sideSpecular;

half2 viewOffset = normalize(worldLightDir + _RimDirection.xyz).xy;
viewOffset.x  = clamp(viewOffset.x, -_RimDirectionRange, _RimDirectionRange);
viewOffset.y  = clamp(viewOffset.y, -_RimDirectionRange, _RimDirectionRange);
half3 viewDir = viewDirectionWS;
viewDir.xy += viewOffset;
half f =  1.0h - saturate(dot(normalize(viewDir), normalWS));
half rim = smoothstep(_RimMin, _RimMax, f);
rim = smoothstep(0, _RimSmooth, rim);
half3 rimColor = rim * _RimMulti * _RimColor.rgb;

half3 emissionColor = _EmissionColor * speclularMap.b;
if(_ADDSUBEMISSION_ON == 1)
{
    half4 subEmissionMap = Texture2DSample(_SubEmissionMap, _SubEmissionMapSampler, uv);
    emissionColor += subEmissionMap.r * _SubEmissionColor1 + subEmissionMap.g * _SubEmissionColor2 + subEmissionMap.b * _SubEmissionColor3;
}

half3 baseLight = diffuse + specular + rimColor + emissionColor;

baseLight = lerp(baseLight , matcapColor, _MatCapLerp * metal);
baseLight += _MatCapRate * metal * matcapColor;//合并matcap到金属部位
baseLight = lerp(baseLight , matcapColor, _MatCapLerp1 * other * speclularMap.r);
baseLight += _MatCapRate1 * other * speclularMap.r * matcapColor;//合并matcap到其他部位
baseLight = lerp(_InsidelineColor, baseLight, insidelineMap);

return baseLight;